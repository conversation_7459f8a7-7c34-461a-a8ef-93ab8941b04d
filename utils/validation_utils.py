#!/usr/bin/env python3
"""
Validation utilities for video files and transcript data
"""

import os
import json
import logging
from typing import Dict, Any, List, Tuple, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class ValidationError(Exception):
    """Custom exception for validation errors"""
    pass


class VideoValidationUtils:
    """Utility class for validating video files and associated transcript data"""
    
    SUPPORTED_VIDEO_FORMATS = ['.mp4', '.mov', '.avi', '.mkv', '.webm']
    REQUIRED_TRANSCRIPT_FILES = ['transcript.json', 'transcript.vtt']
    OPTIONAL_TRANSCRIPT_FILES = ['transcript.txt', 'audio.mp3']
    
    @staticmethod
    def validate_video_file(video_path: str) -> Dict[str, Any]:
        """
        Validate that a video file exists and is in a supported format
        
        Args:
            video_path: Path to the video file
            
        Returns:
            Dictionary with validation results
            
        Raises:
            ValidationError: If validation fails
        """
        result = {
            'valid': False,
            'path': video_path,
            'exists': False,
            'format_supported': False,
            'size_bytes': 0,
            'errors': []
        }
        
        try:
            # Check if file exists
            if not os.path.exists(video_path):
                result['errors'].append(f"Video file does not exist: {video_path}")
                return result
            
            result['exists'] = True
            
            # Check file size
            try:
                result['size_bytes'] = os.path.getsize(video_path)
                if result['size_bytes'] == 0:
                    result['errors'].append("Video file is empty")
                    return result
            except OSError as e:
                result['errors'].append(f"Cannot access video file: {e}")
                return result
            
            # Check file format
            file_ext = Path(video_path).suffix.lower()
            if file_ext not in VideoValidationUtils.SUPPORTED_VIDEO_FORMATS:
                result['errors'].append(f"Unsupported video format: {file_ext}")
                return result
            
            result['format_supported'] = True
            result['valid'] = True
            
            logger.info(f"Video file validation passed: {video_path}")
            return result
            
        except Exception as e:
            result['errors'].append(f"Unexpected error during video validation: {e}")
            logger.error(f"Video validation failed for {video_path}: {e}")
            return result
    
    @staticmethod
    def validate_transcript_directory(transcript_dir: str) -> Dict[str, Any]:
        """
        Validate that transcript directory exists and contains required files
        
        Args:
            transcript_dir: Path to the transcript directory
            
        Returns:
            Dictionary with validation results
        """
        result = {
            'valid': False,
            'path': transcript_dir,
            'exists': False,
            'required_files_present': False,
            'files_found': [],
            'missing_files': [],
            'errors': []
        }
        
        try:
            # Check if directory exists
            if not os.path.exists(transcript_dir):
                result['errors'].append(f"Transcript directory does not exist: {transcript_dir}")
                return result
            
            if not os.path.isdir(transcript_dir):
                result['errors'].append(f"Transcript path is not a directory: {transcript_dir}")
                return result
            
            result['exists'] = True
            
            # Check for required files
            files_in_dir = os.listdir(transcript_dir)
            result['files_found'] = files_in_dir
            
            missing_required = []
            for required_file in VideoValidationUtils.REQUIRED_TRANSCRIPT_FILES:
                if required_file not in files_in_dir:
                    missing_required.append(required_file)
            
            result['missing_files'] = missing_required
            
            if missing_required:
                result['errors'].append(f"Missing required transcript files: {missing_required}")
                return result
            
            result['required_files_present'] = True
            result['valid'] = True
            
            logger.info(f"Transcript directory validation passed: {transcript_dir}")
            return result
            
        except Exception as e:
            result['errors'].append(f"Unexpected error during transcript validation: {e}")
            logger.error(f"Transcript validation failed for {transcript_dir}: {e}")
            return result
    
    @staticmethod
    def validate_transcript_json(json_path: str) -> Dict[str, Any]:
        """
        Validate the transcript JSON file format and content
        
        Args:
            json_path: Path to the transcript JSON file
            
        Returns:
            Dictionary with validation results
        """
        result = {
            'valid': False,
            'path': json_path,
            'parseable': False,
            'has_required_fields': False,
            'duration': 0,
            'language': None,
            'errors': []
        }
        
        try:
            # Try to parse JSON
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            result['parseable'] = True
            
            # Check required fields
            required_fields = ['duration', 'language', 'text']
            missing_fields = []
            
            for field in required_fields:
                if field not in data:
                    missing_fields.append(field)
            
            if missing_fields:
                result['errors'].append(f"Missing required fields in transcript JSON: {missing_fields}")
                return result
            
            result['has_required_fields'] = True
            result['duration'] = data.get('duration', 0)
            result['language'] = data.get('language', 'unknown')
            
            # Validate duration
            if not isinstance(result['duration'], (int, float)) or result['duration'] <= 0:
                result['errors'].append("Invalid duration in transcript JSON")
                return result
            
            # Validate text content
            if not data.get('text') or not isinstance(data['text'], str):
                result['errors'].append("Invalid or missing text content in transcript JSON")
                return result
            
            result['valid'] = True
            logger.info(f"Transcript JSON validation passed: {json_path}")
            return result
            
        except json.JSONDecodeError as e:
            result['errors'].append(f"Invalid JSON format: {e}")
            return result
        except Exception as e:
            result['errors'].append(f"Unexpected error during JSON validation: {e}")
            logger.error(f"Transcript JSON validation failed for {json_path}: {e}")
            return result
    
    @staticmethod
    def validate_sample_directory(sample_dir: str) -> Dict[str, Any]:
        """
        Validate a complete sample directory containing video and transcript data
        
        Args:
            sample_dir: Path to the sample directory (e.g., tests/sample/video1)
            
        Returns:
            Dictionary with comprehensive validation results
        """
        result = {
            'valid': False,
            'sample_dir': sample_dir,
            'video_validation': None,
            'transcript_validation': None,
            'transcript_json_validation': None,
            'errors': []
        }
        
        try:
            # Find video file in the sample directory
            video_file = None
            if os.path.exists(sample_dir):
                for file in os.listdir(sample_dir):
                    file_path = os.path.join(sample_dir, file)
                    if os.path.isfile(file_path):
                        file_ext = Path(file).suffix.lower()
                        if file_ext in VideoValidationUtils.SUPPORTED_VIDEO_FORMATS:
                            video_file = file_path
                            break
            
            if not video_file:
                result['errors'].append(f"No video file found in sample directory: {sample_dir}")
                return result
            
            # Validate video file
            result['video_validation'] = VideoValidationUtils.validate_video_file(video_file)
            if not result['video_validation']['valid']:
                result['errors'].extend(result['video_validation']['errors'])
                return result
            
            # Validate transcript directory
            transcript_dir = os.path.join(sample_dir, 'transcript')
            result['transcript_validation'] = VideoValidationUtils.validate_transcript_directory(transcript_dir)
            if not result['transcript_validation']['valid']:
                result['errors'].extend(result['transcript_validation']['errors'])
                return result
            
            # Validate transcript JSON
            transcript_json = os.path.join(transcript_dir, 'transcript.json')
            result['transcript_json_validation'] = VideoValidationUtils.validate_transcript_json(transcript_json)
            if not result['transcript_json_validation']['valid']:
                result['errors'].extend(result['transcript_json_validation']['errors'])
                return result
            
            result['valid'] = True
            logger.info(f"Sample directory validation passed: {sample_dir}")
            return result
            
        except Exception as e:
            result['errors'].append(f"Unexpected error during sample directory validation: {e}")
            logger.error(f"Sample directory validation failed for {sample_dir}: {e}")
            return result
