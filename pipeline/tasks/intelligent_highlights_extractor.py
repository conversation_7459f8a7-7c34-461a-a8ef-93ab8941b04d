#!/usr/bin/env python3
"""
Intelligent Video Highlights Extractor Task

This task implements an advanced highlights extraction system that analyzes dialogue
transcriptions, computes engagement scores for continuous sentence spans, and generates
precise cut-points for automated highlight reels.

Core Algorithm:
1. Span Analysis: Compute salience scores for every continuous sentence span lasting 5-30 seconds
2. Selection: Pick the highest-scoring spans whose combined duration ≤ TARGET_LEN seconds (default: 75s)
3. Post-processing: Add ±1 second padding, merge overlapping segments, snap to I-frames
4. Output Generation: JSON highlights + FFmpeg concat demuxer file

Scoring Formula:
score = 0.35 × is_QA + 0.25 × keyword_density + 0.25 × emotion_intensity + 0.15 × novelty
"""

import os
import json
import time
import logging
import numpy as np
from typing import Dict, Any, List, Optional
from collections import defaultdict

from pipeline.tasks.base_task import BaseTask
from config.settings import (
    PIPELINE_OUTPUT_DIR,
    OPENAI_HIGHLIGHTS_ENABLED,
    OPENAI_HIGHLIGHTS_MODEL,
    INTELLIGENT_HIGHLIGHTS_MIN_SPAN,
    INTELLIGENT_HIGHLIGHTS_MAX_SPAN,
    OPENAI_HIGHLIGHTS_MAX_REQUESTS,
    OPENAI_HIGHLIGHTS_TOP_CANDIDATES,
)
from utils.highlights_scoring import HighlightsScorer
from utils.iframe_extractor import iframe_extractor
from utils.question_segment_identifier import question_identifier


class IntelligentHighlightsExtractor(BaseTask):
    """
    Advanced Intelligent Video Highlights Extractor

    Implements sophisticated scoring algorithms for automatic highlight detection
    with I-frame boundary snapping and FFmpeg-ready output generation.
    """

    task_name = "intelligent_highlights_extractor"
    requires_gpu = True  # For ML models (sentiment analysis, embeddings)

    def __init__(self):
        super().__init__()
        self.min_span_duration = INTELLIGENT_HIGHLIGHTS_MIN_SPAN
        self.max_span_duration = INTELLIGENT_HIGHLIGHTS_MAX_SPAN
        self.default_target_length = 75.0  # Default target highlight length in seconds
        self.padding_seconds = 1.0     # Padding around spans

        # Initialize enhanced highlights scorer with OpenAI configuration
        self.highlights_scorer = HighlightsScorer(
            openai_enabled=OPENAI_HIGHLIGHTS_ENABLED,
            openai_model=OPENAI_HIGHLIGHTS_MODEL
        )
        self.highlights_scorer.max_openai_requests_per_session = OPENAI_HIGHLIGHTS_MAX_REQUESTS

    def run(self, job_id: str, transcription_result: Dict[str, Any],
            video_ingestor_result: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract intelligent highlights from video transcription

        Args:
            job_id: Unique identifier for the job
            transcription_result: Results from transcription engine
            video_ingestor_result: Results from video ingestor (for video path)
            params: Additional parameters including keywords and target length

        Returns:
            Task result with highlights metadata and file paths
        """
        start_time = time.time()

        try:
            self.logger.info(f"Starting intelligent highlights extraction for job {job_id}")

            # Create output directories
            job_dir = os.path.join(PIPELINE_OUTPUT_DIR, job_id)
            highlights_dir = os.path.join(job_dir, "intelligent_highlights")
            os.makedirs(highlights_dir, exist_ok=True)

            # Get parameters
            keywords = params.get('keywords', [])
            target_length = params.get('target_length', self.default_target_length)

            # Update min/max span duration from params if provided
            # This allows overriding the defaults set in __init__ (which come from config.settings)
            if 'min_duration' in params:
                self.min_span_duration = float(params['min_duration'])
            if 'max_duration' in params:
                self.max_span_duration = float(params['max_duration'])
            self.logger.info(f"Using min_span_duration: {self.min_span_duration}s, max_span_duration: {self.max_span_duration}s")

            self.logger.info(f"Target keywords: {keywords}")
            self.logger.info(f"Target highlight length: {target_length}s")
            self.logger.info(f"OpenAI enhancement enabled: {self.highlights_scorer.openai_enabled}")
            if self.highlights_scorer.openai_enabled:
                self.logger.info(f"OpenAI model: {self.highlights_scorer.openai_model}")
                self.logger.info(f"Max OpenAI requests: {self.highlights_scorer.max_openai_requests_per_session}")

            # Load transcript data
            transcript_path = transcription_result.get('transcript_path')
            if not transcript_path or not os.path.exists(transcript_path):
                raise ValueError(f"Invalid transcript path: {transcript_path}")

            with open(transcript_path, 'r') as f:
                transcript_data = json.load(f)

            segments = transcript_data.get('segments', [])
            if not segments:
                raise ValueError("No transcript segments found")

            self.logger.info(f"Processing {len(segments)} transcript segments")

            # Get video path for I-frame extraction
            video_path = video_ingestor_result.get('optimized_path') or video_ingestor_result.get('video_path')
            if not video_path or not os.path.exists(video_path):
                raise ValueError(f"Invalid video path: {video_path}")

            # Step 1: PRIORITY - Scan entire video for complete Q&A pairs first
            complete_qa_pairs = self._scan_for_complete_qa_pairs(segments, keywords)
            self.logger.info(f"🎯 Found {len(complete_qa_pairs)} complete Q&A pairs in full video")

            # Step 2: If we have good Q&A pairs, prioritize them heavily
            if complete_qa_pairs:
                qa_highlights = self._create_qa_priority_highlights(complete_qa_pairs, target_length)
                self.logger.info(f"✨ Created {len(qa_highlights)} Q&A priority highlights")

                # If Q&A pairs cover most of target duration, use them as primary highlights
                qa_total_duration = sum(qa['duration'] for qa in qa_highlights)
                if qa_total_duration >= target_length * 0.6:  # 60% coverage threshold
                    self.logger.info(f"🚀 Q&A pairs provide {qa_total_duration:.1f}s of {target_length}s target - using Q&A-first approach")
                    return self._finalize_qa_first_result(job_id, qa_highlights, complete_qa_pairs, start_time, params, highlights_dir, video_path)

            # Step 3: Fallback to traditional approach if insufficient Q&A content
            self.logger.info("📺 Insufficient Q&A content, falling back to traditional highlights approach")

            # Identify all question-based segments throughout the video
            question_segments = question_identifier.identify_all_question_segments(segments)
            self.logger.info(f"Identified {len(question_segments)} question-based segments")

            # Generate continuous sentence spans with question prioritization
            spans = self._generate_sentence_spans(segments, question_segments)
            self.logger.info(f"Generated {len(spans)} sentence spans")

            # Score each span using multi-component algorithm with question boost
            scored_spans = self._score_spans(spans, segments, keywords, question_segments)
            self.logger.info(f"Scored {len(scored_spans)} spans")

            # Apply quality filters
            filtered_spans = self._apply_quality_filters(scored_spans, segments)
            self.logger.info(f"Filtered to {len(filtered_spans)} high-quality spans")

            # Select optimal spans within target duration
            selected_spans = self._select_optimal_spans(filtered_spans, target_length)
            self.logger.info(f"Selected {len(selected_spans)} optimal spans")

            # Add padding and merge overlapping segments
            padded_spans = self._add_padding_and_merge(selected_spans)
            self.logger.info(f"Merged to {len(padded_spans)} final spans")

            # Step 6: Extract I-frame timestamps and snap boundaries
            iframe_timestamps = iframe_extractor.extract_iframe_timestamps(video_path)
            snapped_highlights = iframe_extractor.snap_highlights_to_iframes(padded_spans, iframe_timestamps)

            # Step 7: Generate outputs
            outputs = self._generate_outputs(snapped_highlights, video_path, highlights_dir, job_id)

            # Calculate total duration
            total_duration = sum(h['duration'] for h in snapped_highlights)

            execution_time = time.time() - start_time

            result = {
                'status': 'completed',
                'highlights_count': len(snapped_highlights),
                'total_duration': total_duration,
                'target_duration': target_length,
                'highlights_path': outputs['highlights_json'],
                'concat_file_path': outputs['concat_file'],
                'iframe_count': len(iframe_timestamps),
                'execution_time': execution_time,
                'metadata': {
                    'keywords': keywords,
                    'spans_generated': len(spans),
                    'spans_scored': len(scored_spans),
                    'spans_filtered': len(filtered_spans),
                    'spans_selected': len(selected_spans),
                    'final_highlights': len(snapped_highlights),
                    'question_segments_identified': len(question_segments),
                    'qa_spans_in_final': sum(1 for h in snapped_highlights if h.get('contains_qa', False)),
                    'avg_qa_priority_boost': sum(h.get('qa_priority_boost', 0) for h in snapped_highlights) / len(snapped_highlights) if snapped_highlights else 0
                }
            }

            # Save state
            self.save_state(job_id, result, self.task_name)

            self.logger.info(f"Intelligent highlights extraction completed in {execution_time:.2f}s")
            self.logger.info(f"Generated {len(snapped_highlights)} highlights with total duration {total_duration:.1f}s")

            return result

        except Exception as e:
            self.logger.error(f"Error in intelligent highlights extraction: {str(e)}")
            error_result = {
                'status': 'failed',
                'error': str(e),
                'execution_time': time.time() - start_time
            }
            self.save_state(job_id, error_result, self.task_name)
            return error_result

    def _generate_sentence_spans(self, segments: List[Dict[str, Any]],
                                question_segments: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """
        Generate continuous sentence spans lasting 5-30 seconds with question prioritization

        Args:
            segments: List of transcript segments
            question_segments: List of identified question-based segments

        Returns:
            List of sentence spans with metadata
        """
        spans = []
        question_segments = question_segments or []

        # Create a set of question segment indices for quick lookup
        question_indices = set()
        for qs in question_segments:
            question_indices.update(range(qs['question_idx'], qs['answer_idx'] + 1))

        for start_idx in range(len(segments)):
            current_duration = 0.0
            start_time = segments[start_idx]['start']

            for end_idx in range(start_idx, len(segments)):
                end_time = segments[end_idx]['end']
                current_duration = end_time - start_time

                # Check if span is within valid duration range
                if current_duration >= self.min_span_duration:
                    if current_duration <= self.max_span_duration:
                        # Create span
                        span_text = ' '.join(seg['text'] for seg in segments[start_idx:end_idx+1])

                        # Check if this span contains question-answer content
                        contains_qa = any(idx in question_indices for idx in range(start_idx, end_idx + 1))

                        # Find the best matching question segment if any
                        matching_qa = None
                        if contains_qa:
                            for qs in question_segments:
                                if (qs['question_idx'] >= start_idx and qs['answer_idx'] <= end_idx):
                                    matching_qa = qs
                                    break

                        span = {
                            'start_time': start_time,
                            'end_time': end_time,
                            'duration': current_duration,
                            'start_segment_idx': start_idx,
                            'end_segment_idx': end_idx,
                            'text': span_text,
                            'segment_count': end_idx - start_idx + 1,
                            'contains_qa': contains_qa,
                            'matching_qa': matching_qa,
                            'qa_priority_boost': 0.0  # Will be calculated in scoring
                        }
                        spans.append(span)
                    else:
                        # Span too long, break
                        break

        # Ensure minimum duration in spans
        spans = [span for span in spans if span.get('duration', 0) >= self.min_span_duration]

        return spans

    def _score_spans(self, spans: List[Dict[str, Any]], segments: List[Dict[str, Any]],
                    keywords: List[str], question_segments: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """
        Score each span using multi-component algorithm with question prioritization

        Args:
            spans: List of sentence spans
            segments: Original transcript segments
            keywords: Target keywords for relevance scoring
            question_segments: List of identified question-based segments

        Returns:
            List of spans with computed scores
        """
        scored_spans = []
        question_segments = question_segments or []

        # Pre-compute Q&A patterns for all segments
        qa_scores = self.highlights_scorer.detect_qa_patterns(segments)

        # Process spans with progress logging
        total_spans = len(spans)
        for i, span in enumerate(spans):
            # Log progress every 100 spans
            if i % 100 == 0:
                self.logger.info(f"Processing span {i+1}/{total_spans} ({(i+1)/total_spans*100:.1f}%)")

            start_idx = span['start_segment_idx']
            end_idx = span['end_segment_idx']
            span_text = span['text']

            # Component 1: Q&A Detection (35%)
            qa_score = max(qa_scores.get(i, 0.0) for i in range(start_idx, end_idx + 1))

            # Component 2: Keyword Density (25%)
            keyword_density = self.highlights_scorer.calculate_keyword_density(span_text, keywords)

            # Component 3: Emotion Intensity (25%)
            emotion_intensity = self.highlights_scorer.calculate_emotion_intensity(span_text)

            # Component 4: Novelty (15%)
            # Get context from previous 60 seconds
            context_texts = self._get_context_texts(segments, start_idx, 60.0)
            novelty = self.highlights_scorer.calculate_novelty_score(span_text, context_texts)

            # Calculate base composite score
            composite_score = self.highlights_scorer.calculate_composite_score(
                qa_score, keyword_density, emotion_intensity, novelty
            )

            # Apply question-based prioritization boost
            qa_priority_boost = self._calculate_qa_priority_boost(span, question_segments)

            # Enhanced composite score with question prioritization
            # Apply boost multiplicatively to preserve relative scoring
            enhanced_score = composite_score * (1.0 + qa_priority_boost)

            # Add scoring details to span
            scored_span = span.copy()
            scored_span.update({
                'qa_score': qa_score,
                'keyword_density': keyword_density,
                'emotion_intensity': emotion_intensity,
                'novelty': novelty,
                'composite_score': enhanced_score,
                'base_composite_score': composite_score,
                'qa_priority_boost': qa_priority_boost,
                'score_density': enhanced_score / span['duration']  # Score per second
            })

            scored_spans.append(scored_span)

        # Sort by score density (score per second)
        scored_spans.sort(key=lambda x: x['score_density'], reverse=True)

        # Cost-effective OpenAI enhancement: only process top candidates
        if self.highlights_scorer.openai_enabled and len(scored_spans) > 0:
            scored_spans = self._enhance_top_spans_with_openai(scored_spans, keywords)

        # Log OpenAI usage summary
        if self.highlights_scorer.openai_enabled:
            self.logger.info(f"OpenAI requests used: {self.highlights_scorer.openai_request_count}/{self.highlights_scorer.max_openai_requests_per_session}")

        return scored_spans

    def _enhance_top_spans_with_openai(self, scored_spans: List[Dict[str, Any]],
                                      keywords: List[str]) -> List[Dict[str, Any]]:
        """
        Cost-effective OpenAI enhancement: only process top candidates

        Args:
            scored_spans: List of scored spans sorted by score_density
            keywords: Target keywords

        Returns:
            Enhanced scored spans with OpenAI improvements for top candidates
        """
        if not self.highlights_scorer.openai_enabled:
            return scored_spans

        # Only enhance top N spans to control costs (configurable)
        top_candidates_count = min(OPENAI_HIGHLIGHTS_TOP_CANDIDATES, len(scored_spans))

        self.logger.info(f"Enhancing top {top_candidates_count} spans with OpenAI")

        enhanced_spans = scored_spans.copy()

        for i in range(top_candidates_count):
            span = enhanced_spans[i]
            span_text = span['text']

            try:
                # Enhance keyword relevance
                openai_keyword_score = self.highlights_scorer._enhance_keyword_relevance_with_openai(
                    span_text, keywords
                )

                # Enhance content quality
                openai_quality_score = self.highlights_scorer._enhance_content_quality_with_openai(
                    span_text
                )

                # Combine with existing scores
                original_score = span['composite_score']

                # Weighted combination: 70% original, 20% keyword enhancement, 10% quality
                enhanced_score = (
                    0.7 * original_score +
                    0.2 * openai_keyword_score +
                    0.1 * openai_quality_score
                )

                # Update the span
                enhanced_spans[i]['composite_score'] = enhanced_score
                enhanced_spans[i]['score_density'] = enhanced_score / span['duration']
                enhanced_spans[i]['openai_enhanced'] = True
                enhanced_spans[i]['openai_keyword_score'] = openai_keyword_score
                enhanced_spans[i]['openai_quality_score'] = openai_quality_score

                # Stop if we hit the request limit
                if self.highlights_scorer.openai_request_count >= self.highlights_scorer.max_openai_requests_per_session:
                    self.logger.warning(f"Reached OpenAI request limit, enhanced {i+1} spans")
                    break

            except Exception as e:
                self.logger.warning(f"Failed to enhance span {i} with OpenAI: {e}")
                continue

        # Re-sort after enhancement
        enhanced_spans.sort(key=lambda x: x['score_density'], reverse=True)

        return enhanced_spans

    def _calculate_qa_priority_boost(self, span: Dict[str, Any],
                                   question_segments: List[Dict[str, Any]]) -> float:
        """
        Calculate question-answer priority boost for a span

        Args:
            span: The span to evaluate
            question_segments: List of identified question segments

        Returns:
            Priority boost factor (0.0 to 1.0)
        """
        if not question_segments or not span.get('contains_qa', False):
            return 0.0

        max_boost = 0.0

        # Check if span contains complete Q&A pairs
        for qs in question_segments:
            # Calculate overlap between span and question segment
            span_start = span['start_segment_idx']
            span_end = span['end_segment_idx']
            qa_start = qs['question_idx']
            qa_end = qs['answer_idx']

            # Check for overlap
            overlap_start = max(span_start, qa_start)
            overlap_end = min(span_end, qa_end)

            if overlap_start <= overlap_end:
                # Calculate overlap ratio
                overlap_segments = overlap_end - overlap_start + 1
                qa_segments = qa_end - qa_start + 1
                overlap_ratio = overlap_segments / qa_segments

                # Base boost based on overlap completeness
                base_boost = 0.3 * overlap_ratio  # Up to 30% boost for complete overlap

                # Additional boost for high-quality Q&A pairs
                quality_boost = 0.2 * qs.get('quality_score', 0.0)  # Up to 20% for quality
                engagement_boost = 0.2 * qs.get('engagement_score', 0.0)  # Up to 20% for engagement
                priority_boost = 0.3 * qs.get('priority_score', 0.0)  # Up to 30% for priority

                total_boost = base_boost + quality_boost + engagement_boost + priority_boost
                max_boost = max(max_boost, total_boost)

        return min(1.0, max_boost)  # Cap at 100% boost

    def _get_context_texts(self, segments: List[Dict[str, Any]], current_idx: int,
                          window_seconds: float) -> List[str]:
        """
        Get context texts from previous segments within time window

        Args:
            segments: List of transcript segments
            current_idx: Current segment index
            window_seconds: Time window in seconds

        Returns:
            List of context text strings (limited to max 10 segments for efficiency)
        """
        if current_idx == 0:
            return []

        current_time = segments[current_idx]['start']
        context_texts = []
        max_context_segments = 10  # Limit context size for performance

        for i in range(current_idx - 1, -1, -1):
            segment = segments[i]
            if current_time - segment['start'] > window_seconds:
                break
            context_texts.insert(0, segment['text'])

            # Limit context size to prevent memory issues
            if len(context_texts) >= max_context_segments:
                break

        return context_texts

    def _apply_quality_filters(self, spans: List[Dict[str, Any]],
                              segments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Apply quality filters to spans

        Args:
            spans: List of scored spans
            segments: Original transcript segments

        Returns:
            List of filtered spans
        """
        filtered_spans = []

        for span in spans:
            start_idx = span['start_segment_idx']
            end_idx = span['end_segment_idx']

            # Check quality for all segments in span
            passes_quality = True
            for i in range(start_idx, end_idx + 1):
                segment = segments[i]
                if not self.highlights_scorer.apply_quality_filters(segment):
                    passes_quality = False
                    break

            if passes_quality:
                filtered_spans.append(span)

        # Ensure minimum duration in filtered spans
        filtered_spans = [span for span in filtered_spans if span.get('duration', 0) >= self.min_span_duration]

        return filtered_spans

    def _select_optimal_spans(self, spans: List[Dict[str, Any]],
                             target_length: float) -> List[Dict[str, Any]]:
        """
        Select optimal spans within target duration with question prioritization

        Args:
            spans: List of filtered and scored spans
            target_length: Target total duration in seconds

        Returns:
            List of selected spans
        """
        selected_spans = []
        total_duration = 0.0

        # Separate question-based spans for priority selection
        qa_spans = [span for span in spans if span.get('contains_qa', False)]
        non_qa_spans = [span for span in spans if not span.get('contains_qa', False)]

        # Sort both lists by score density
        qa_spans.sort(key=lambda x: x['score_density'], reverse=True)
        non_qa_spans.sort(key=lambda x: x['score_density'], reverse=True)

        self.logger.info(f"Prioritizing {len(qa_spans)} question-based spans out of {len(spans)} total spans")

        # First pass: Select high-quality question-based spans
        qa_budget = min(target_length * 0.7, target_length)  # Reserve up to 70% for Q&A content
        for span in qa_spans:
            span_duration = span['duration']

            if total_duration + span_duration <= qa_budget:
                if not self._has_overlap(span, selected_spans):
                    selected_spans.append(span)
                    total_duration += span_duration
                    self.logger.debug(f"Selected Q&A span: {span['start_time']:.1f}s-{span['end_time']:.1f}s "
                                    f"(score: {span['score_density']:.3f}, boost: {span.get('qa_priority_boost', 0):.2f})")

        # Second pass: Fill remaining time with best non-Q&A spans
        for span in non_qa_spans:
            span_duration = span['duration']

            if total_duration + span_duration <= target_length:
                if not self._has_overlap(span, selected_spans):
                    selected_spans.append(span)
                    total_duration += span_duration

                    if total_duration >= target_length * 0.95:  # 95% of target
                        break

        # Sort selected spans by start time for chronological order
        selected_spans.sort(key=lambda x: x['start_time'])

        qa_count = sum(1 for span in selected_spans if span.get('contains_qa', False))
        self.logger.info(f"Selected {qa_count} question-based spans out of {len(selected_spans)} total spans")

        # Ensure minimum duration in selected spans
        selected_spans = [span for span in selected_spans if span.get('duration', 0) >= self.min_span_duration]

        return selected_spans

    def _has_overlap(self, span: Dict[str, Any], existing_spans: List[Dict[str, Any]]) -> bool:
        """
        Check if span overlaps with any existing spans

        Args:
            span: Span to check
            existing_spans: List of already selected spans

        Returns:
            True if overlap exists, False otherwise
        """
        span_start = span['start_time']
        span_end = span['end_time']

        for existing in existing_spans:
            existing_start = existing['start_time']
            existing_end = existing['end_time']

            # Check for overlap
            if not (span_end <= existing_start or span_start >= existing_end):
                return True

        return False

    def _add_padding_and_merge(self, spans: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Add padding to spans and merge overlapping segments

        Args:
            spans: List of selected spans

        Returns:
            List of padded and merged spans
        """
        if not spans:
            return []

        # Add padding to each span
        padded_spans = []
        for span in spans:
            padded_span = span.copy()
            padded_span['start_time'] = max(0.0, span['start_time'] - self.padding_seconds)
            padded_span['end_time'] = span['end_time'] + self.padding_seconds
            padded_span['duration'] = padded_span['end_time'] - padded_span['start_time']
            padded_spans.append(padded_span)

        # Sort by start time
        padded_spans.sort(key=lambda x: x['start_time'])

        # Merge overlapping spans
        merged_spans = []
        current_span = padded_spans[0]

        for next_span in padded_spans[1:]:
            if next_span['start_time'] <= current_span['end_time']:
                # Merge spans
                current_span['end_time'] = max(current_span['end_time'], next_span['end_time'])
                current_span['duration'] = current_span['end_time'] - current_span['start_time']

                # Combine scores (weighted average by duration)
                total_duration = current_span['duration'] + next_span['duration']
                current_span['composite_score'] = (
                    (current_span['composite_score'] * current_span['duration'] +
                     next_span['composite_score'] * next_span['duration']) / total_duration
                )
                current_span['score_density'] = current_span['composite_score'] / current_span['duration']
            else:
                # No overlap, add current span and move to next
                merged_spans.append(current_span)
                current_span = next_span

        # Add the last span
        merged_spans.append(current_span)

        return merged_spans

    def _generate_outputs(self, highlights: List[Dict[str, Any]], video_path: str,
                         output_dir: str, job_id: str) -> Dict[str, str]:
        """
        Generate output files (JSON highlights and FFmpeg concat file)

        Args:
            highlights: List of final highlight spans
            video_path: Path to source video
            output_dir: Output directory
            job_id: Job identifier

        Returns:
            Dictionary with output file paths
        """
        # Generate JSON highlights file
        highlights_json_path = os.path.join(output_dir, "highlights.json")

        # Prepare highlights data for JSON output
        json_highlights = []
        for i, highlight in enumerate(highlights):
            json_highlight = {
                'start_time': highlight['start_time'],
                'end_time': highlight['end_time'],
                'duration': highlight['duration'],
                'score': highlight['composite_score'],
                'score_density': highlight['score_density'],
                'text': highlight.get('text', ''),
                'components': {
                    'qa_score': highlight.get('qa_score', 0.0),
                    'keyword_density': highlight.get('keyword_density', 0.0),
                    'emotion_intensity': highlight.get('emotion_intensity', 0.0),
                    'novelty': highlight.get('novelty', 0.0)
                },
                'metadata': {
                    'segment_count': highlight.get('segment_count', 0),
                    'original_start_time': highlight.get('original_start_time'),
                    'original_end_time': highlight.get('original_end_time'),
                    'start_adjustment': highlight.get('start_adjustment', 0.0),
                    'end_adjustment': highlight.get('end_adjustment', 0.0),
                    'contains_qa': highlight.get('contains_qa', False),
                    'qa_priority_boost': highlight.get('qa_priority_boost', 0.0),
                    'base_composite_score': highlight.get('base_composite_score', highlight.get('score', 0.0))
                }
            }
            json_highlights.append(json_highlight)

        # Save JSON highlights with numpy type handling
        self._save_json_with_numpy_support(json_highlights, highlights_json_path)

        # Generate FFmpeg concat file
        concat_file_path = os.path.join(output_dir, "cuts.txt")
        iframe_extractor.generate_ffmpeg_concat_file(highlights, video_path, concat_file_path)

        self.logger.info(f"Generated highlights JSON: {highlights_json_path}")
        self.logger.info(f"Generated FFmpeg concat file: {concat_file_path}")

        return {
            'highlights_json': highlights_json_path,
            'concat_file': concat_file_path
        }

    def _save_json_with_numpy_support(self, data: Any, file_path: str) -> None:
        """
        Save data as JSON with numpy type conversion

        Args:
            data: Data to save
            file_path: Path to save the JSON file
        """
        import numpy as np

        def convert_numpy_types(obj):
            """Recursively convert numpy types to Python types"""
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {key: convert_numpy_types(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            else:
                return obj

        # Convert numpy types
        converted_data = convert_numpy_types(data)

        # Save to file
        with open(file_path, 'w') as f:
            json.dump(converted_data, f, indent=2)

    def _scan_for_complete_qa_pairs(self, segments: List[Dict[str, Any]], keywords: List[str]) -> List[Dict[str, Any]]:
        """
        Scan entire video transcript to identify complete question-answer pairs

        Args:
            segments: List of transcript segments
            keywords: Target keywords for relevance scoring

        Returns:
            List of complete Q&A pairs with timing and quality scores
        """
        import re

        qa_pairs = []

        # Question patterns - comprehensive detection
        question_patterns = [
            r'\b(?:what|how|why|when|where|who|which)\b[^.!?]*\?',  # Direct questions
            r'\b(?:can|could|would|will|do|does|did|is|are|was|were)\b[^.!?]*\?',  # Yes/no questions
            r'\b(?:tell me|explain|describe|elaborate on)\b[^.!?]*[.!?]',  # Imperative questions
            r'\b(?:what do you think|what are your thoughts|how do you feel)\b[^.!?]*[.!?]'  # Opinion questions
        ]

        self.logger.info(f"Scanning {len(segments)} segments for complete Q&A pairs...")

        for i, segment in enumerate(segments):
            text = segment.get('text', '').strip()
            if not text:
                continue

            # Check if this segment contains a question
            is_question = False
            question_text = ""

            for pattern in question_patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    question_candidate = match.group().strip()
                    if len(question_candidate) > 10:  # Filter very short questions
                        is_question = True
                        question_text = question_candidate
                        break
                if is_question:
                    break

            if not is_question:
                continue

            # Look for answer in subsequent segments (within next 30 seconds)
            answer_segments = []
            answer_text = ""
            question_end_time = segment.get('end', 0)

            for j in range(i + 1, min(i + 10, len(segments))):  # Look ahead up to 10 segments
                answer_segment = segments[j]
                answer_start_time = answer_segment.get('start', 0)

                # Stop if gap is too large (more than 30 seconds)
                if answer_start_time - question_end_time > 30.0:
                    break

                answer_segments.append(answer_segment)
                answer_text += " " + answer_segment.get('text', '').strip()

                # Stop if we have a substantial answer (at least 20 words)
                if len(answer_text.split()) >= 20:
                    break

            if not answer_text.strip() or len(answer_text.split()) < 5:
                continue  # Skip if no substantial answer found

            # Calculate timing and duration
            question_start = segment.get('start', 0)
            question_end = segment.get('end', 0)
            answer_start = answer_segments[0].get('start', 0) if answer_segments else question_end
            answer_end = answer_segments[-1].get('end', 0) if answer_segments else answer_start

            total_duration = answer_end - question_start

            # Filter by duration (10-30 seconds for optimal social media clips)
            if total_duration < self.min_span_duration or total_duration > self.max_span_duration:
                continue

            # Calculate quality score based on multiple factors
            quality_score = self._calculate_qa_pair_quality(question_text, answer_text, keywords)

            qa_pair = {
                'question_text': question_text,
                'answer_text': answer_text.strip(),
                'question_start': question_start,
                'question_end': question_end,
                'answer_start': answer_start,
                'answer_end': answer_end,
                'total_start': question_start,
                'total_end': answer_end,
                'duration': total_duration,
                'quality_score': quality_score,
                'question_segment_idx': i,
                'answer_segment_indices': [segments.index(seg) for seg in answer_segments],
                'keyword_relevance': self._calculate_keyword_relevance(question_text + " " + answer_text, keywords)
            }

            qa_pairs.append(qa_pair)

        # Sort by quality score and keyword relevance
        qa_pairs.sort(key=lambda x: (x['quality_score'] * x['keyword_relevance']), reverse=True)

        self.logger.info(f"Found {len(qa_pairs)} complete Q&A pairs")
        return qa_pairs

    def _calculate_qa_pair_quality(self, question: str, answer: str, keywords: List[str]) -> float:
        """Calculate quality score for a Q&A pair"""
        score = 0.0

        # Question quality factors
        question_lower = question.lower()
        if any(word in question_lower for word in ['what', 'how', 'why']):
            score += 0.3  # High-value question words

        if any(word in question_lower for word in ['think', 'believe', 'opinion', 'feel']):
            score += 0.2  # Opinion/thought-provoking questions

        # Answer quality factors
        answer_words = len(answer.split())
        if 15 <= answer_words <= 100:  # Optimal answer length
            score += 0.3
        elif 10 <= answer_words <= 150:
            score += 0.2
        elif answer_words >= 5:
            score += 0.1

        # Keyword relevance
        combined_text = (question + " " + answer).lower()
        keyword_matches = sum(1 for keyword in keywords if keyword.lower() in combined_text)
        if keyword_matches > 0:
            score += min(0.2, keyword_matches * 0.1)  # Up to 0.2 for keyword relevance

        return min(1.0, score)

    def _calculate_keyword_relevance(self, text: str, keywords: List[str]) -> float:
        """Calculate keyword relevance score"""
        if not keywords:
            return 0.5  # Neutral score if no keywords

        text_lower = text.lower()
        matches = sum(1 for keyword in keywords if keyword.lower() in text_lower)
        return min(1.0, matches / len(keywords) + 0.3)  # Base score + keyword matches

    def _create_qa_priority_highlights(self, qa_pairs: List[Dict[str, Any]], target_length: float) -> List[Dict[str, Any]]:
        """Create highlights prioritizing Q&A pairs"""
        highlights = []
        total_duration = 0.0

        for qa_pair in qa_pairs:
            if total_duration + qa_pair['duration'] <= target_length:
                highlight = {
                    'start_time': qa_pair['total_start'],
                    'end_time': qa_pair['total_end'],
                    'duration': qa_pair['duration'],
                    'text': qa_pair['question_text'] + " " + qa_pair['answer_text'],
                    'question': qa_pair['question_text'],
                    'answer': qa_pair['answer_text'],
                    'score': qa_pair['quality_score'],
                    'composite_score': qa_pair['quality_score'],  # Add composite_score for I-frame snapping
                    'final_score': qa_pair['quality_score'],  # Add final_score for compatibility
                    'score_density': qa_pair['quality_score'] / qa_pair['duration'],  # Add score_density
                    'type': 'qa_pair',
                    'qa_data': qa_pair,
                    'contains_qa': True,  # Mark as containing Q&A
                    'qa_priority_boost': 1.0,  # High priority boost for Q&A-first highlights
                    # Add additional fields for compatibility with traditional highlights
                    'qa_score': qa_pair['quality_score'],
                    'keyword_density': qa_pair.get('keyword_relevance', 0.5),
                    'emotion_intensity': 0.5,  # Default emotion intensity
                    'novelty': 0.5,  # Default novelty score
                    'base_composite_score': qa_pair['quality_score']
                }
                highlights.append(highlight)
                total_duration += qa_pair['duration']

        return highlights

    def _finalize_qa_first_result(self, job_id: str, qa_highlights: List[Dict[str, Any]],
                                 complete_qa_pairs: List[Dict[str, Any]], start_time: float,
                                 params: Dict[str, Any], highlights_dir: str, video_path: str) -> Dict[str, Any]:
        """Finalize result when using Q&A-first approach"""
        from utils.iframe_extractor import iframe_extractor

        # Extract I-frame timestamps and snap boundaries
        iframe_timestamps = iframe_extractor.extract_iframe_timestamps(video_path)
        snapped_highlights = iframe_extractor.snap_highlights_to_iframes(qa_highlights, iframe_timestamps)

        # Generate outputs
        outputs = self._generate_outputs(snapped_highlights, video_path, highlights_dir, job_id)

        # Calculate total duration
        total_duration = sum(h['duration'] for h in snapped_highlights)
        execution_time = time.time() - start_time

        result = {
            'status': 'completed',
            'highlights_count': len(snapped_highlights),
            'total_duration': total_duration,
            'target_duration': params.get('target_length', self.default_target_length),
            'highlights_path': outputs['highlights_json'],
            'concat_file_path': outputs['concat_file'],
            'iframe_count': len(iframe_timestamps),
            'execution_time': execution_time,
            'qa_first_approach': True,  # Flag to indicate Q&A-first was used
            'complete_qa_pairs_found': len(complete_qa_pairs),
            'metadata': {
                'keywords': params.get('keywords', []),
                'qa_pairs_identified': len(complete_qa_pairs),
                'qa_highlights_selected': len(snapped_highlights),
                'approach': 'qa_first',
                'qa_coverage_ratio': total_duration / params.get('target_length', self.default_target_length)
            }
        }

        # Save state
        self.save_state(job_id, result, self.task_name)

        self.logger.info(f"🎯 Q&A-first highlights extraction completed in {execution_time:.2f}s")
        self.logger.info(f"✨ Generated {len(snapped_highlights)} Q&A highlights with total duration {total_duration:.1f}s")

        return result


# Create a task instance
intelligent_highlights_extractor = IntelligentHighlightsExtractor()


def extract_intelligent_highlights(job_id: str, *args, **kwargs) -> Dict[str, Any]:
    """
    Run the intelligent highlights extractor task

    Args:
        job_id: Unique identifier for the job
        *args: Additional positional arguments
        **kwargs: Additional keyword arguments

    Returns:
        Task result
    """
    return intelligent_highlights_extractor.run(job_id, *args, **kwargs)
